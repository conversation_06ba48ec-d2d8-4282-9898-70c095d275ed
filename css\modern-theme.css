/* Modern Theme CSS - Updated Color Scheme and Smooth Styling */

:root {
  /* New Color Scheme */
  --primary-color: #4f67a5;
  --secondary-color: #299679;
  --primary-hover: #3d5287;
  --secondary-hover: #1f7a61;
  --primary-light: #6b7db8;
  --secondary-light: #33b08a;
  
  /* Neutral Colors */
  --text-dark: #2c3e50;
  --text-light: #ffffff;
  --text-muted: #6c757d;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-light: #e9ecef;
  --shadow-light: rgba(79, 103, 165, 0.1);
  --shadow-medium: rgba(79, 103, 165, 0.15);
  --shadow-dark: rgba(79, 103, 165, 0.25);
  
  /* Modern Spacing and Borders */
  --border-radius: 12px;
  --border-radius-small: 8px;
  --border-radius-large: 20px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Override existing color variables */
:root {
  --clr-primary: var(--primary-color) !important;
  --clr-primary-hover: var(--primary-hover) !important;
  --clr-primary-dark: var(--primary-color) !important;
}

/* Global Smooth Transitions */
* {
  transition: var(--transition-fast);
}

/* Modern Button Styling */
.explore-btn,
.cta-button,
.contact-btn,
.fd-cta-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
  border: none !important;
  border-radius: var(--border-radius) !important;
  box-shadow: 0 4px 15px var(--shadow-light) !important;
  transition: var(--transition) !important;
  font-weight: 600 !important;
  text-transform: none !important;
  letter-spacing: 0.5px !important;
}

.explore-btn:hover,
.cta-button:hover,
.contact-btn:hover,
.fd-cta-button:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px var(--shadow-medium) !important;
}

/* Modern Card Styling */
.service-item,
.gallery-item,
.about-content {
  border-radius: var(--border-radius) !important;
  box-shadow: 0 4px 20px var(--shadow-light) !important;
  transition: var(--transition) !important;
  overflow: hidden !important;
}

.service-item:hover,
.gallery-item:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 12px 35px var(--shadow-medium) !important;
}

/* Modern Hero Section */
.hero {
  background: linear-gradient(135deg, rgba(79, 103, 165, 0.5) 0%, rgba(41, 150, 121, 0.4) 100%),
              url(../img/header-image.webp) center/cover !important;
}

.text-overlay {
  background: linear-gradient(to right,
              rgba(79, 103, 165, 0.6) 0%,
              rgba(79, 103, 165, 0.4) 50%,
              rgba(79, 103, 165, 0.15) 80%,
              transparent 100%) !important;
}

/* Modern Service Icons */
.service-image svg {
  stroke: var(--secondary-color) !important;
  transition: var(--transition) !important;
}

.service-item:hover .service-image svg {
  stroke: var(--secondary-hover) !important;
  transform: scale(1.1) !important;
}

/* Modern About Section */
.about-us {
  background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%) !important;
}

.about-image img {
  border-radius: var(--border-radius-large) !important;
  box-shadow: 0 8px 30px var(--shadow-medium) !important;
  transition: var(--transition) !important;
}

.about-image img:hover {
  transform: scale(1.02) !important;
  box-shadow: 0 12px 40px var(--shadow-dark) !important;
}

/* Modern Navigation */
#main-nav {
  backdrop-filter: blur(10px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border-bottom: 1px solid var(--border-light) !important;
}

.desktop-menu a {
  color: var(--text-dark) !important;
  font-weight: 500 !important;
  transition: var(--transition) !important;
  position: relative !important;
}

.desktop-menu a:hover {
  color: var(--primary-color) !important;
}

.desktop-menu a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transition: var(--transition);
}

.desktop-menu a:hover::after {
  width: 100%;
}

/* Modern Gallery */
.gallery-item {
  position: relative !important;
  overflow: hidden !important;
}

.gallery-item .overlay {
  background: linear-gradient(135deg, 
              rgba(79, 103, 165, 0.8) 0%, 
              rgba(41, 150, 121, 0.8) 100%) !important;
  opacity: 0 !important;
  transition: var(--transition) !important;
}

.gallery-item:hover .overlay {
  opacity: 1 !important;
}

/* Modern Contact Section */
.contact-item {
  background: var(--background-white) !important;
  border-radius: var(--border-radius) !important;
  padding: 1.5rem !important;
  box-shadow: 0 4px 15px var(--shadow-light) !important;
  transition: var(--transition) !important;
  margin-bottom: 1rem !important;
}

.contact-item:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px var(--shadow-medium) !important;
}

.contact-icon {
  color: var(--secondary-color) !important;
  transition: var(--transition) !important;
}

.contact-item:hover .contact-icon {
  color: var(--secondary-hover) !important;
  transform: scale(1.1) !important;
}

/* Modern Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-dark) !important;
  font-weight: 600 !important;
}

.h6 {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-light);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--secondary-hover));
}

/* Modern Focus States */
*:focus {
  outline: 2px solid var(--primary-color) !important;
  outline-offset: 2px !important;
}

/* Modern Loading Animation */
@keyframes modernPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.loading {
  animation: modernPulse 2s infinite;
}

/* Modern Responsive Improvements */
@media (max-width: 768px) {
  :root {
    --border-radius: 8px;
    --border-radius-large: 12px;
  }
  
  .service-item,
  .contact-item {
    margin-bottom: 1rem !important;
  }
}
