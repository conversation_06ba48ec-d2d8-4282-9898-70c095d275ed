<!DOCTYPE html>
<html lang="pt">
  <head>
    <meta charset="utf-8" />
    <title>
      Lugesmar Catering & Eventos – Comida Deliciosa e Experiências
      Inesquecíveis
    </title>
    <meta
      name="description"
      content="Celebre os momentos especiais da vida com a Lugesmar Unipessoal, Limitada – o seu parceiro de confiança para catering, planeamento de eventos e serviços de restauração deliciosos. Desde casamentos a eventos corporativos, criamos experiências inesquecíveis com um serviço excecional e pratos de fazer crescer água na boca. Contacte-nos hoje mesmo!"
    />

    <meta name="keywords" content="serviços de catering" />

    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <!-- Favicon -->
    <link rel="icon" href="./favicon/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="./favicon/apple-touch-icon.jpg"
    />
    <link
      rel="icon"
      type="image/jpg"
      sizes="32x32"
      href="./favicon/favicon-32x32.jpg"
    />
    <link
      rel="icon"
      type="image/jpg"
      sizes="16x16"
      href="./favicon/favicon-16x16.jpg"
    />
    <link rel="manifest" href="./favicon/site.webmanifest.json" />
    <!-- Robots -->
    <meta name="robots" content="index, follow" />
    <!-- Site Published Date -->
    <meta property="article:published_time" content="2025-26-06" />
    <!-- Google Verification -->
    <!-- <meta name="google-site-verification" content="Your Google Search Console Verification Code"> -->

    <!-- Google Verification -->
    <meta
      name="google-site-verification"
      content="Nfa81_h3ktKoyM-OShPPIX_9lm-mxtEed74zlQB9Eog"
    />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="lugesmar.paginasamarelas.cv" />
    <meta
      property="og:title"
      content="Lugesmar Catering & Eventos – Comida Deliciosa e Experiências
      Inesquecíveis"
    />
    <meta
      property="og:description"
      content="Deseja boa comida e memórias únicas? No Padjudo há sabor, calor humano e alegria com quem mais ama. Venha viver o trio: comida, amizade e riso!"
    />
    <meta
      property="og:image"
      content="lugesmar.paginasamarelas.cv/img/logo.webp"
    />
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:site" content="@yellowpages238" />
    <meta property="twitter:url" content="lugesmar.paginasamarelas.cv " />
    <meta
      property="twitter:title"
      content="Lugesmar Catering & Eventos – Comida Deliciosa e Experiências
      Inesquecíveis "
    />
    <meta
      property="twitter:description"
      content="Deseja boa comida e memórias únicas? No Padjudo há sabor, calor humano e alegria com quem mais ama. Venha viver o trio: comida, amizade e riso!"
    />
    <meta
      property="twitter:image"
      content="lugesmar.paginasamarelas.cv/img/logo.webp"
    />
    <!-- Canonical URL -->
    <link rel="canonical" href="lugesmar.paginasamarelas.cv" />
    <!-- Hreflang tags -->
    <link rel="alternate" hreflang="en" href="lugesmar.paginasamarelas.cv" />
    <!-- Include more hreflang tags here if you have the website available in other languages -->
    <!-- Sitemap -->
    <link
      rel="sitemap"
      type="application/xml"
      title="Sitemap"
      href="lugesmar.paginasamarelas.cv/sitemap.xml"
    />

    <!-- Preconnect to Google Maps APIs -->
    <link rel="preconnect" href="https://maps.googleapis.com" crossorigin />
    <link rel="preconnect" href="https://maps.gstatic.com" crossorigin />

    <link rel="preload" as="image" href="./img/header-image.webp" />
    <!-- <link rel="preload" as="image" href="./img/hero_tablet.webp" />
    <link rel="preload" as="image" href="./img/hero_mobile.webp" /> -->

    <!-- Internal CSS -->

    <link rel="stylesheet" href="css/features.css" />
    <link rel="stylesheet" href="css/ots.css" />
    <link rel="stylesheet" href="css/s2.css" />
    <link rel="stylesheet" href="css/hero.css" />
    <link rel="stylesheet" href="css/service-section.css" />
    <link rel="stylesheet" href="css/mn.css" />
    <link rel="stylesheet" href="css/about.css" />
    <link rel="stylesheet" href="css/cta.css" />
    <link rel="stylesheet" href="css/testimonial.css" />
    <link rel="stylesheet" href="css/main.css" />
    <link rel="stylesheet" href="css/gallary.css" />
    <link rel="stylesheet" href="css/modern-theme.css" />

    <link rel="alternate" hreflang="en" href="lugesmar.paginasamarelas.cv/" />
    <link
      rel="alternate"
      hreflang="x-default"
      href="lugesmar.paginasamarelas.cv/"
    />

    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-H7Z33316MM"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());
      gtag("config", "G-H7Z33316MM");
    </script>

    <style>
      html {
        scroll-behavior: smooth;
      }

      /* Inline critical font styles for Poppins */
      @font-face {
        font-family: "Poppins";
        font-style: normal;
        font-weight: 400;
        src: local("Poppins Regular"), local("Poppins-Regular"),
          url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2)
            format("woff2");
        font-display: swap;
      }

      @font-face {
        font-family: "Poppins";
        font-style: normal;
        font-weight: 700;
        src: local("Poppins Bold"), local("Poppins-Bold"),
          url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2)
            format("woff2");
        font-display: swap;
      }

      /* Inline critical font styles for Work Sans */

      @font-face {
        font-family: "Work Sans";
        font-style: normal;
        font-weight: 400;
        src: local("Work Sans Regular"), local("WorkSans-Regular"),
          url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2)
            format("woff2");
        font-display: swap;
      }

      @font-face {
        font-family: "Work Sans";
        font-style: normal;
        font-weight: 600;
        src: local("Work Sans SemiBold"), local("WorkSans-SemiBold"),
          url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2)
            format("woff2");
        font-display: swap;
      }

      @font-face {
        font-family: "Work Sans";
        font-style: normal;
        font-weight: 700;
        src: local("Work Sans Bold"), local("WorkSans-Bold"),
          url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2)
            format("woff2");
        font-display: swap;
      }

      body {
        font-family: "Work Sans", sans-serif;
      }

      .mobile-menu {
        transition: transform 0.3s ease-in-out;
      }

      .mobile-menu.hidden {
        transform: translateX(-100%);
      }

      #top-bar {
        transition: transform 0.3s ease-out, opacity 0.3s ease-out;
      }

      #main-nav {
        transition: all 0.3s ease-out;
      }

      .sticky {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 50;
        background-color: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(79, 103, 165, 0.1);
      }

      /* Enhanced smooth scrolling */
      html {
        scroll-behavior: smooth;
      }

      /* Modern loading states */
      img {
        transition: opacity 0.3s ease;
      }

      img[loading="lazy"] {
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
      }

      @keyframes fadeIn {
        to { opacity: 1; }
      }

      /* Enhanced focus states for accessibility */
      a:focus, button:focus {
        outline: 2px solid #4f67a5;
        outline-offset: 2px;
        border-radius: 4px;
      }
    </style>
  </head>

  <body>
    <div class="content-grid" data-theme="blue">
      <div id="top-bar">
        <div class="top-bar-inner">
          <div class="contact-info-topbar">
            <a
              href="mailto:<EMAIL>?subject=Pedido%20de%20Orçamento%20solicitado%20através%20das%20Páginas%20Amarelas%20de%20Cabo%20Verde"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon"
              >
                <path
                  d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                ></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
              <EMAIL>
            </a>
            <span class="separator"></span>
            <span class="working-hours">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 1 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>

              Boa Morte
            </span>
          </div>
          <div class="phone-and-social">
            <div class="phone-number phon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                ></path>
              </svg>
              <a href="tel:9883308">9883308</a>
            </div>
            <div class="social-icons">
              <!-- linkedin-->
              <a
                href="https://www.facebook.com/juhmonteiro20photography/
    "
                target="_blank"
                class="social-icon"
                aria-label="linkedin"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="currentColor"
                    d="m16.117 8.906l-2.589 3.086l-3.715-2.281a.5.5 0 0 0-.644.104l-3.052 3.636a.5.5 0 0 0 .766.643l2.774-3.306l3.715 2.281c.211.13.485.085.645-.104l2.866-3.416a.5.5 0 0 0-.766-.643M12 1C5.715 1 .975 5.594.975 11.686a10.43 10.43 0 0 0 3.471 7.905c.071.06.114.149.118.242l.058 1.867a1.343 1.343 0 0 0 1.883 1.187l2.088-.92a.33.33 0 0 1 .226-.018c1.037.283 2.107.425 3.181.422c6.285 0 11.025-4.594 11.025-10.685S18.285 1 12 1m0 20.371a11 11 0 0 1-2.916-.387a1.36 1.36 0 0 0-.894.068l-2.086.919a.35.35 0 0 1-.324-.026a.34.34 0 0 1-.158-.276l-.058-1.871a1.34 1.34 0 0 0-.45-.952a9.45 9.45 0 0 1-3.14-7.16C1.975 6.164 6.285 2 12 2s10.025 4.164 10.025 9.686S17.715 21.37 12 21.37"
                  />
                </svg>
              </a>

              <!-- Whatsapp -->
              <a
                href="https://api.whatsapp.com/send/?phone=9883308&text&type=phone_number&app_absent=0"
                target="_blank"
                class="social-icon"
                aria-label="whatsapp"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                >
                  <g
                    fill="none"
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    color="currentColor"
                  >
                    <path
                      d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.25.018.58.157 1.24.436A10 10 0 0 0 12 22"
                    />
                    <path
                      d="m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87"
                    />
                  </g>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Navigation -->
    <nav id="main-nav" class="content-grid">
      <div class="nav-inner">
        <div class="logo">
          <img src="./img/lugesmar-logo.jpeg" alt="Logo" title="Logo" width="100" height="70">
          <div class="logo">
            <!-- <a href="/" class="logo-text"
              >Lugesmar Catering & Eventos </a
            > -->
          </div>
        </div>
        <div class="desktop-menu">
          <a href="/">Home</a>
          <a href="#about">Sobre nós</a>
          <a href="#services">Serviços</a>
          <a href="#contact">Contactos</a>
        </div>
        <button id="mobile-menu-toggle" class="mobile-menu-toggle">
          &#9776;
        </button>
        <a
          href="mailto:<EMAIL>?subject=Pedido%20de%20Orçamento%20solicitado%20através%20das%20Páginas%20Amarelas%20de%20Cabo%20Verde"
          class="contact-btn"
          >Entre em contacto</a
        >
      </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu" class="mobile-menu">
      <div class="mobile-menu-content">
        <div class="menu-header">
          <img src="./img/lugesmar-logo.jpeg" alt="Logo" title="Logo">

          <!-- <a href="/" class="logo-text"
            >Lugesmar Catering & Eventos</a
          > -->

          <button id="close-mobile-menu">&times;</button>
        </div>
        <div class="menu-links">
          <a href="/">Home</a>
          <a href="#about">Sobre nós</a>
          <a href="#services">Serviços</a>
          <a href="#contact">Contactos</a>

          <!-- button mobile menu -->
          <a
            href="mailto:<EMAIL>?subject=Pedido%20de%20Orçamento%20solicitado%20através%20das%20Páginas%20Amarelas%20de%20Cabo%20Verde"
            class="contact-btn"
            >Entre em contacto</a
          >
        </div>
      </div>
    </div>

    <!-- Hero Section -->
    <section class="hero">
      <div class="text-overlay"></div>
      <div class="content-grid">
        <div class="hero-content">
          <div>
            <span class="h6">Lugesmar Catering & Eventos</span>
            <h1> Transformamos Momentos em Experiências Inesquecíveis.</h1>
            <p>
              Especialistas em catering, planeamento de eventos e restauração,
                unimos sabor, criatividade e serviço excecional para tornar cada
                ocasião memorável.
            </p>
            <a href="#services" class="explore-btn mt" style="color: #fff"
              >Ver Serviços</a
            >
          </div>
        </div>
      </div>
      <!-- <div class="decorative-element"></div> -->
    </section>

    <div class="about-us" id="about">
      <div class="content-grid">
        <div class="about-content">
          <div class="about-image" style="max-width: 80%; display: block;">
            <img
              src="./img/about-us.webp"
              title="about_us"
              alt="about_us"
              loading="lazy"
              width="600"
              height="600"
              style="width: 100%; height: auto; max-width: none;"
            />
          </div>
          <div class="about-text">
            <span class="abt">Sobre Nós</span>

            <p class="abt-dsec">
              Mais do que um restaurante — uma extensão da sua casa
            </p>

            <p>
              Na Lugesmar Unipessoal, Limitada, transformamos bons momentos em experiências inesquecíveis.
Especializados em catering, planeamento de eventos e retalho em geral, reunimos comida deliciosa, ideias criativas e um serviço excecional sob o mesmo teto. 
Quer esteja a organizar uma celebração especial, a planear um evento corporativo ou simplesmente à procura de refeições e produtos de qualidade, nós temos o que precisa. Da nossa cozinha à sua mesa, e do conceito à conclusão, cuidamos de cada detalhe. Visite-nos hoje mesmo

            </p>


            <a href="#services" target="_blank" class="cta-button"
              >Explorar mais</a
            >
          </div>
        </div>
      </div>
    </div>

    <div class="fd-cta-container">
      <div class="fd-image-section">
        <div
          class="fd-image-bg lazy-background"
          data-bg="./img/filler.webp"
        ></div>
        <div class="fd-image-overlay"></div>
      </div>

      <div class="fd-content-section">
        <div class="fd-patterny-overlay"></div>

        <div>
          <h2>Descubra a Experiência Lugesmar.</h2>
          <p>
            Assista ao nosso vídeo e conheça como transformamos bons momentos em experiências inesquecíveis.
Da cozinha à mesa, do planeamento à celebração, mostramos a paixão, criatividade e dedicação que tornam cada evento único.


          </p>

          <a
            href="mailto:<EMAIL>?subject=Pedido%20de%20Orçamento%20solicitado%20através%20das%20Páginas%20Amarelas%20de%20Cabo%20Verde"
            class="fd-cta-button"
            >Reserve agora <span class="fd-arrow">→</span></a
          >
        </div>
      </div>
    </div>

    <div class="content-grid" id="services">
      <div class="services-header">
        <h2 class="s2-section-title">Nossos Serviços</h2>
        <p>Oferecemos uma gama completa de serviços para tornar os seus eventos e experiências gastronómicas verdadeiramente especiais.</p>
      </div>

      <div class="services-grid" style="max-width: 1400px">
        <div class="service-item">
          <div class="service-image" style="display: flex; justify-content: center; align-items: center; height: 80px; margin-bottom: 10px; padding: 0;">
            <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#299679" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"/>
              <path d="M7 2v20"/>
              <path d="M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"/>
            </svg>
          </div>
          <div class="service-content">
            <h3>Serviços de catering</h3>
            <p>
              De reuniões íntimas a grandes celebrações, oferecemos menus de dar água na boca, adaptados ao seu gosto. Ingredientes frescos, apresentação profissional e sabores inesquecíveis são a nossa marca registada.
            </p>
          </div>
        </div>
        <div class="service-item">
          <div class="service-image" style="display: flex; justify-content: center; align-items: center; height: 80px; margin-bottom: 10px; padding: 0;">
            <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#299679" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M8 2v4"/>
              <path d="M16 2v4"/>
              <rect width="18" height="18" x="3" y="4" rx="2"/>
              <path d="M3 10h18"/>
              <path d="M8 14h.01"/>
              <path d="M12 14h.01"/>
              <path d="M16 14h.01"/>
              <path d="M8 18h.01"/>
              <path d="M12 18h.01"/>
              <path d="M16 18h.01"/>
            </svg>
          </div>
          <div class="service-content">
            <h3>Planeamento e gestão de eventos</h3>
            <p>
              Tratamos de tudo, desde a conceção do conceito até ao brinde final. Seja um casamento, um evento corporativo ou uma festa privada, tornamos a sua visão realidade com estilo e precisão.
            </p>
          </div>
        </div>
        <div class="service-item">
          <div class="service-image" style="display: flex; justify-content: center; align-items: center; height: 80px; margin-bottom: 10px; padding: 0;">
            <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#299679" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12V7a5 5 0 0 1 10 0v5"/>
              <rect width="14" height="10" x="5" y="12" rx="2"/>
              <circle cx="12" cy="17" r="1"/>
            </svg>
          </div>
          <div class="service-content">
            <h3>Restaurante</h3>
            <p>
              Desfrute de um ambiente acolhedor, serviço atencioso e uma variedade de pratos preparados na hora para satisfazer todos os paladares.
            </p>
          </div>
        </div>

        <div class="service-item">
          <div class="service-image" style="display: flex; justify-content: center; align-items: center; height: 80px; margin-bottom: 10px; padding: 0;">
            <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#299679" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="8" cy="21" r="1"/>
              <circle cx="19" cy="21" r="1"/>
              <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/>
            </svg>
          </div>
          <div class="service-content">
            <h3>Venda de alimentos</h3>
            <p>
              Oferecemos uma seleção de refeições frescas e prontas a consumir, snacks e alimentos especiais para particulares e empresas.
            </p>
          </div>
        </div>

        <div class="service-item">
          <div class="service-image" style="display: flex; justify-content: center; align-items: center; height: 80px; margin-bottom: 10px; padding: 0;">
            <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#299679" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"/>
              <path d="M3 6h18"/>
              <path d="M16 10a4 4 0 0 1-8 0"/>
            </svg>
          </div>
          <div class="service-content">
            <h3>Venda a retalho</h3>
            <p>
              Além de alimentos, fornecemos uma gama de produtos de qualidade para satisfazer as suas necessidades diárias.
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="content-grid">
      <div class="gallery-header">
        <h2 class="products-section-title">Gallery</h2>
      </div>
      <div class="gallery">
        <div class="gallery-item">
          <img
            src="./img/01.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="688"
            height="384"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/02.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="332"
            height="180"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/03.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="332"
            height="180"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/04.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="688"
            height="180"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/05.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="332"
            height="384"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/06.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="332"
            height="180"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/07.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="688"
            height="384"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/08.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="332"
            height="180"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/09.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="688"
            height="384"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/10.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="332"
            height="180"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/11.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="332"
            height="180"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/12.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="688"
            height="180"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/13.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="332"
            height="384"
          />
          <div class="overlay"></div>
        </div>
        <div class="gallery-item">
          <img
            src="./img/14.webp"
            alt="Bar Restaurante Padjudo"
            title="Bar Restaurante Padjudo"
            width="332"
            height="180"
          />
          <div class="overlay"></div>
        </div>
      </div>
    </div>

    <section class="contact" id="contact">
      <div class="content-grid">
        <h2 class="footer-section-title">Contact Us</h2>
        <div class="contact-content">
          <div class="contact-map">
           <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d245334.05497622918!2d-22.815419799999997!3d16.09946465!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x94b25fe6e90c16f%3A0x2d7a17a397e16ab3!2sBoa%20Vista!5e0!3m2!1sen!2ske!4v1755670895640!5m2!1sen!2ske"
                  width="100%"
                  height="450"
                  style="border: 0"
                  allowfullscreen=""
                  loading="lazy"
                  referrerpolicy="no-referrer-when-downgrade"
                >
                </iframe>
          </div>
          <div class="contact-info">
            <div class="contact-item">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.5rem"
                  height="1.5rem"
                  viewBox="0 0 512 512"
                  class="contact-icon"
                >
                  <path
                    fill="currentColor"
                    d="M256 0C158.75 0 80 78.75 80 176C80 267.4 256 512 256 512C256 512 432 267.4 432 176C432 78.75 353.25 0 256 0zM256 240C220.65 240 192 211.35 192 176C192 140.65 220.65 112 256 112C291.35 112 320 140.65 320 176C320 211.35 291.35 240 256 240z"
                  ></path>
                </svg>
              </span>
              <div>
                <h3>Endereço físico</h3>
                <p>Boa Morte</p>
              </div>
            </div>
            <div class="contact-item">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.5rem"
                  height="1.5rem"
                  viewBox="0 0 512 512"
                  class="contact-icon"
                >
                  <path
                    fill="currentColor"
                    d="m497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6"
                  ></path>
                </svg>
              </span>
              <div>
                <h3>Número de telefone</h3>
                <p><a href="tel:9883308">9883308</a></p>
              </div>
            </div>
            <div class="contact-item">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.5rem"
                  height="1.5rem"
                  viewBox="0 0 512 512"
                  class="contact-icon"
                >
                  <path
                    fill="currentColor"
                    d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7c22.4 17.4 52.1 39.5 154.1 113.6c21.1 15.4 56.7 47.8 92.2 47.6c35.7.3 72-32.8 92.3-47.6c102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4c132.7-96.3 142.8-104.7 173.4-128.7c5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9c30.6 23.9 40.7 32.4 173.4 128.7c16.8 12.2 50.2 41.8 73.4 41.4"
                  ></path></svg
              ></span>
              <div>
                <h3>E-mail</h3>
                <p>
                  <a
                    href="mailto:<EMAIL>?subject=Pedido%20de%20Orçamento%20solicitado%20através%20das%20Páginas%20Amarelas%20de%20Cabo%20Verde"
                    ><EMAIL></a
                  >
                </p>
              </div>
            </div>
            <div class="contact-item">
              <span
                ><svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.5rem"
                  height="1.5rem"
                  viewBox="0 0 512 512"
                  class="contact-icon"
                >
                  <path
                    fill="currentColor"
                    d="M256 0a256 256 0 1 1 0 512a256 256 0 1 1 0-512m-24 120v136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24"
                  ></path></svg
              ></span>
              <div>
                <h3>Horário de funcionamento</h3>
                <p>Segunda a sábado Loja: 7h30 – 22h30</p>
                <p>Restaurante: 10h00 – 22h30</p>
              </div>
            </div>
            <!-- Social Media Icons -->
            <div class="contact-item">
              <span
                ><svg
                  class="contact-icon"
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.5rem"
                  height="1.5rem"
                  viewBox="0 0 16 16"
                >
                  <path
                    fill="currentColor"
                    d="M12 10c-.8 0-1.4.3-2 .8L6.8 9c.1-.3.2-.7.2-1s-.1-.7-.2-1L10 5.2c.6.5 1.2.8 2 .8c1.7 0 3-1.3 3-3s-1.3-3-3-3s-3 1.3-3 3v.5L5.5 5.4C5.1 5.2 4.6 5 4 5C2.4 5 1 6.3 1 8c0 1.6 1.4 3 3 3c.6 0 1.1-.2 1.5-.4L9 12.5v.5c0 1.7 1.3 3 3 3s3-1.3 3-3s-1.3-3-3-3"
                  /></svg
              ></span>

              <div>
                <h3>Ligue-se a Nós</h3>

                <div class="social-links">
                  <!-- Fb -->
                  <a
                    href="https://www.facebook.com/juhmonteiro20photography/"
                    target="_blank"
                    class="social-link"
                    aria-label="Linkedin"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                    >
                      <path
                        fill="currentColor"
                        d="m16.117 8.906l-2.589 3.086l-3.715-2.281a.5.5 0 0 0-.644.104l-3.052 3.636a.5.5 0 0 0 .766.643l2.774-3.306l3.715 2.281c.211.13.485.085.645-.104l2.866-3.416a.5.5 0 0 0-.766-.643M12 1C5.715 1 .975 5.594.975 11.686a10.43 10.43 0 0 0 3.471 7.905c.071.06.114.149.118.242l.058 1.867a1.343 1.343 0 0 0 1.883 1.187l2.088-.92a.33.33 0 0 1 .226-.018c1.037.283 2.107.425 3.181.422c6.285 0 11.025-4.594 11.025-10.685S18.285 1 12 1m0 20.371a11 11 0 0 1-2.916-.387a1.36 1.36 0 0 0-.894.068l-2.086.919a.35.35 0 0 1-.324-.026a.34.34 0 0 1-.158-.276l-.058-1.871a1.34 1.34 0 0 0-.45-.952a9.45 9.45 0 0 1-3.14-7.16C1.975 6.164 6.285 2 12 2s10.025 4.164 10.025 9.686S17.715 21.37 12 21.37"
                      />
                    </svg>
                  </a>

                  <!-- Whatsapp -->
                  <a
                    href="https://api.whatsapp.com/send/?phone=9883308&text&type=phone_number&app_absent=0"
                    target="_blank"
                    class="social-link"
                    aria-label="Whatsapp"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                    >
                      <g
                        fill="none"
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1.5"
                        color="currentColor"
                      >
                        <path
                          d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.25.018.58.157 1.24.436A10 10 0 0 0 12 22"
                        />
                        <path
                          d="m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87"
                        />
                      </g>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <footer class="footer full-width">
      <div class="content-grid">
        <div class="footer-content">
          <div class="copyright">
            <p>
              &copy; <span id="current-year"></span> 
Lugesmar Catering & Eventos
              Todos os direitos reservados.
            </p>
          </div>
          <div class="designer">
            <a
              href="https://www.paginasamarelas.cv/en"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img
                src="./img/yp_logo.webp"
                loading="lazy"
                alt="Paginas Amarelas Cabo Verde"
                width="50"
                height="50"
                title="Paginas Amarelas Cabo Verde"
              />
              <p>Desenvolvido por Paginas Amarelas Cabo Verde</p>
            </a>
          </div>
        </div>
      </div>
    </footer>

    <!-- <script src="./js/testimonial.js"></script> -->
    <!-- <script src="./js/main.js"></script> -->

    <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"></script>

    <script>
      document.getElementById("current-year").textContent =
        new Date().getFullYear();
    </script>

    <script>
      // Initialize VanillaTilt for 3D card effect
      VanillaTilt.init(document.querySelectorAll(".card"), {
        max: 5,
        speed: 400,
        glare: true,
        "max-glare": 0.2,
      });

      // Intersection Observer for scroll animations
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.style.opacity = 1;
              entry.target.style.transform = "translateY(0)";
            }
          });
        },
        {
          threshold: 0.1,
        }
      );

      // Observe all cards
      document.querySelectorAll(".card").forEach((card) => {
        card.style.opacity = 0;
        card.style.transform = "translateY(20px)";
        observer.observe(card);
      });
    </script>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const topBar = document.getElementById("top-bar");
        const mainNav = document.getElementById("main-nav");
        const mainContent = document.querySelector("body"); // Adjust this selector if needed
        const mobileMenu = document.getElementById("mobile-menu");
        const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
        const closeMobileMenu = document.getElementById("close-mobile-menu");
        const mobileMenuLinks = document.querySelectorAll(
          ".mobile-menu .menu-links a"
        );

        // Mobile Menu Logic
        mobileMenuToggle.addEventListener("click", () => {
          mobileMenu.classList.add("show");
        });

        closeMobileMenu.addEventListener("click", () => {
          mobileMenu.classList.remove("show");
        });

        // Add click event listeners to all mobile menu links
        mobileMenuLinks.forEach((link) => {
          link.addEventListener("click", function (event) {
            mobileMenu.classList.remove("show");

            const href = this.getAttribute("href");
            if (href.startsWith("#") && href !== "#") {
              event.preventDefault();

              const targetElement = document.querySelector(href);

              if (targetElement) {
                setTimeout(() => {
                  const yOffset = -80;
                  const y =
                    targetElement.getBoundingClientRect().top +
                    window.pageYOffset +
                    yOffset;

                  window.scrollTo({
                    top: y,
                    behavior: "smooth",
                  });
                }, 300);
              }
            }
          });
        });

        // Debug function to log sticky state
        function logStickyState() {
          //console.log('Scroll position:', window.scrollY);
          //console.log('mainNav has sticky class:', mainNav.classList.contains('sticky'));
          //console.log('mainNav style:', mainNav.style.cssText);
          //console.log('computed position:', window.getComputedStyle(mainNav).position);
        }

        // Improved Sticky Header Logic
        function handleScroll() {
          const scrollTop =
            window.scrollY || document.documentElement.scrollTop;
          //console.log('Scrolling, position:', scrollTop);

          if (scrollTop > 50) {
            // Make sure we're applying direct styles
            mainNav.style.position = "fixed";
            mainNav.style.top = "0";
            mainNav.style.left = "0";
            mainNav.style.width = "100%";
            mainNav.style.zIndex = "100";
            mainNav.style.boxShadow = "0 2px 10px rgba(0, 0, 0, 0.1)";
            mainNav.classList.add("sticky");

            // Add padding to body to prevent content jump
            mainContent.style.paddingTop = mainNav.offsetHeight + "px";

            // Hide the top bar
            if (topBar) {
              topBar.style.display = "none";
            }

            logStickyState();
          } else {
            // Remove direct styles
            mainNav.style.position = "";
            mainNav.style.top = "";
            mainNav.style.left = "";
            mainNav.style.width = "";
            mainNav.style.zIndex = "";
            mainNav.style.boxShadow = "";
            mainNav.classList.remove("sticky");

            // Remove padding from body
            mainContent.style.paddingTop = "0";

            // Show the top bar on desktop
            if (topBar && window.innerWidth >= 1024) {
              topBar.style.display = "block";
            }

            logStickyState();
          }
        }

        // Initial check on page load
        handleScroll();

        // Add scroll event listener
        window.addEventListener("scroll", handleScroll);

        // Handle window resize
        window.addEventListener("resize", () => {
          if (window.innerWidth < 1024 && topBar) {
            topBar.style.display = "none";
          } else if (window.scrollY <= 50 && topBar) {
            topBar.style.display = "block";
          }

          // Recalculate sticky state on resize
          handleScroll();
        });
      });
    </script>

    <script>
      function throttle(fn, limit) {
        let waiting = false;
        return function (...args) {
          if (!waiting) {
            fn.apply(this, args);
            waiting = true;
            setTimeout(() => (waiting = false), limit);
          }
        };
      }

      function handleParallaxScroll() {
        const elements = document.querySelectorAll("[data-parallax]");
        const scrollY = window.scrollY;

        elements.forEach((el) => {
          const container = el.closest(".parallax-container");
          const rect = container.getBoundingClientRect();
          const offsetTop = container.offsetTop;
          const height = container.offsetHeight;

          // Only calculate if it's in view
          if (
            scrollY + window.innerHeight > offsetTop &&
            scrollY < offsetTop + height
          ) {
            const speed = 0.5; // Adjust this to control intensity
            const yPos = (scrollY - offsetTop) * speed;
            el.style.transform = `translateY(${yPos}px)`;
          }
        });
      }

      document.addEventListener("DOMContentLoaded", function () {
        window.addEventListener("scroll", throttle(handleParallaxScroll, 16)); // 60fps-ish
      });
    </script>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const lazySections = document.querySelectorAll(".lazy-background");

        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                const el = entry.target;
                const bgUrl = el.dataset.bg;

                // Create a new image to preload the background
                const img = new Image();
                img.src = bgUrl;

                img.onload = function () {
                  // Only set background when fully loaded
                  el.style.backgroundImage = `url('${bgUrl}')`;
                  el.classList.add("loaded");
                };

                // Stop observing this element
                observer.unobserve(el);
              }
            });
          },
          {
            rootMargin: "200px", // Preload a bit before the element enters the viewport
            threshold: 0.1,
          }
        );

        lazySections.forEach((section) => observer.observe(section));
      });
    </script>
  </body>
</html>
